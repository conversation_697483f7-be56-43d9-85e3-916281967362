import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  query,
  where,
  serverTimestamp,
  writeBatch,
  Timestamp
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { Product, ProductVariant } from '../types';
import {
  BulkProductRow,
  BulkImportResult,
  BulkImportError,
  BulkImportWarning,
  BulkImportOptions,
  BulkExportOptions
} from '../types/bulkProduct';
import { validateProductRow } from '../utils/csvUtils';
import { getAllProducts } from './productService';
import { getProductVariants, addProductVariant, updateProductVariant } from './productVariantService';
import { getAllCategories } from './productService';
import { categoryPathToId } from './categoryPathService';

// Collection names
const PRODUCTS_COLLECTION = 'Products';

// Check if Barcode exists in database
export const checkBarcodeExists = async (barcode: string, excludeProductId?: string): Promise<boolean> => {
  try {
    const productsRef = collection(db, PRODUCTS_COLLECTION);
    const q = query(productsRef, where('barcode', '==', barcode));
    const querySnapshot = await getDocs(q);

    // If excluding a product ID, check if any other product has this barcode
    if (excludeProductId) {
      return querySnapshot.docs.some(doc => doc.id !== excludeProductId);
    }

    return !querySnapshot.empty;
  } catch (error) {
    console.error('Error checking barcode existence:', error);
    return false;
  }
};

// Get product by Barcode
export const getProductByBarcode = async (barcode: string): Promise<Product | null> => {
  try {
    const productsRef = collection(db, PRODUCTS_COLLECTION);
    const q = query(productsRef, where('barcode', '==', barcode));
    const querySnapshot = await getDocs(q);

    if (!querySnapshot.empty) {
      const doc = querySnapshot.docs[0];
      const data = doc.data();

      return {
        id: doc.id,
        name: data.name || '',
        description: data.description || '',
        category: data.categoryID || '',
        categoryIDs: data.categoryIDs || [],
        primaryCategoryID: data.primaryCategoryID || '',
        images: data.image ? [data.image] : [],
        price: data.price || 0,
        mrp: data.mrp || 0,
        stock: data.maxQuantity || 0,
        unit: data.unit || 'kg',
        barcode: data.barcode || '',
        isVisible: data.status === 'active',
        status: data.status === 'active' ? 'available' : 'out_of_stock',
        farmerId: data.farmerId || '',
        nutrition: data.nutrition || '',
        storageInstruction: data.storageInstruction || '',
        hasVariants: data.hasVariants || false,
        defaultVariantID: data.defaultVariantID || '',
        isPreOrder: data.isPreOrder || false,
        preOrderStartAt: data.preOrderStartAt,
        preOrderEndAt: data.preOrderEndAt,
        harvestOffsetDays: data.harvestOffsetDays || 0,
        createdAt: data.createdAt ? new Date(data.createdAt.toDate()).toISOString() : new Date().toISOString(),
        updatedAt: data.updatedAt ? new Date(data.updatedAt.toDate()).toISOString() : new Date().toISOString(),
      } as Product;
    }

    return null;
  } catch (error) {
    console.error('Error getting product by SKU:', error);
    return null;
  }
};

// Convert BulkProductRow to Product
const convertRowToProduct = async (row: BulkProductRow): Promise<Omit<Product, 'id' | 'createdAt' | 'updatedAt'>> => {
  // Resolve category path to category ID if provided
  let categoryId = row.category || row.primaryCategoryID;
  let categoryIDs = row.categoryIDs ? row.categoryIDs.split(',').map(id => id.trim()).filter(Boolean) : [];

  if (row.categoryPath?.trim()) {
    try {
      const resolvedCategoryId = await categoryPathToId(row.categoryPath);
      if (resolvedCategoryId) {
        categoryId = resolvedCategoryId;
        // If no explicit categoryIDs provided, use the resolved category
        if (categoryIDs.length === 0) {
          categoryIDs = [resolvedCategoryId];
        }
      }
    } catch (error) {
      console.error('Error resolving category path:', error);
    }
  }

  return {
    productId: row.productId,
    name: row.name,
    description: row.description,
    price: row.price,
    mrp: row.mrp,
    stock: row.stock,
    unit: row.unit,
    barcode: row.barcode,
    status: row.status,
    isVisible: row.isVisible,
    category: categoryId,
    categoryIDs,
    primaryCategoryID: categoryId,
    farmerId: row.farmerId,
    nutrition: row.nutrition,
    storageInstruction: row.storageInstruction,
    hasVariants: row.hasVariants,
    defaultVariantID: row.defaultVariantID,
    isPreOrder: row.isPreOrder,
    preOrderStartAt: row.preOrderStartAt ? new Date(row.preOrderStartAt) : null,
    preOrderEndAt: row.preOrderEndAt ? new Date(row.preOrderEndAt) : null,
    harvestOffsetDays: row.harvestOffsetDays,
    images: row.images ? row.images.split(',').map(url => url.trim()).filter(Boolean) : []
  };
};

// Convert BulkProductRow to ProductVariant
const convertRowToVariant = (row: BulkProductRow, productId: string, productNumericId: number): Omit<ProductVariant, 'id' | 'createdAt' | 'updatedAt'> => {
  let variationValues = {};

  if (row.variationValues) {
    try {
      variationValues = JSON.parse(row.variationValues);
    } catch (error) {
      console.error('Error parsing variation values:', error);
    }
  }

  return {
    productId,
    productNumericId,
    barcode: row.barcode,
    variationValues,
    price: row.price,
    mrp: row.mrp,
    stock: row.stock,
    image: row.variantImage || '',
    status: row.status === 'available' ? 'active' : 'inActive',
    isPreOrder: row.isPreOrder ? row.isPreOrder : null,
    preOrderStartAt: row.preOrderStartAt ? new Date(row.preOrderStartAt) : null,
    preOrderEndAt: row.preOrderEndAt ? new Date(row.preOrderEndAt) : null,
    harvestOffsetDays: row.harvestOffsetDays !== undefined ? row.harvestOffsetDays : null
  };
};

// Validate categories and farmers exist
export const validateReferences = async (rows: BulkProductRow[]): Promise<{
  validCategories: Set<string>;
  validFarmers: Set<string>;
  errors: BulkImportError[];
}> => {
  const errors: BulkImportError[] = [];

  try {
    // Get all categories and farmers
    const [categories, farmers] = await Promise.all([
      getAllCategories(),
      getAllFarmers()
    ]);

    const validCategories = new Set(categories.map(c => c.id));
    const validFarmers = new Set(farmers.map(f => f.id));

    // Validate references in rows
    rows.forEach((row, index) => {
      // Validate category references
      if (row.category && !validCategories.has(row.category)) {
        errors.push({
          row: index + 1,
          field: 'category',
          message: `Category ID '${row.category}' does not exist`
        });
      }

      if (row.categoryIDs) {
        const categoryIds = row.categoryIDs.split(',').map(id => id.trim()).filter(Boolean);
        categoryIds.forEach(categoryId => {
          if (!validCategories.has(categoryId)) {
            errors.push({
              row: index + 1,
              field: 'categoryIDs',
              message: `Category ID '${categoryId}' does not exist`
            });
          }
        });
      }

      if (row.primaryCategoryID && !validCategories.has(row.primaryCategoryID)) {
        errors.push({
          row: index + 1,
          field: 'primaryCategoryID',
          message: `Primary category ID '${row.primaryCategoryID}' does not exist`
        });
      }

      // Validate farmer reference
      if (row.farmerId && !validFarmers.has(row.farmerId)) {
        errors.push({
          row: index + 1,
          field: 'farmerId',
          message: `Farmer ID '${row.farmerId}' does not exist`
        });
      }
    });

    return { validCategories, validFarmers, errors };
  } catch (error) {
    console.error('Error validating references:', error);
    return {
      validCategories: new Set(),
      validFarmers: new Set(),
      errors: [{
        row: 0,
        message: 'Failed to validate category and farmer references'
      }]
    };
  }
};

// Import products from CSV data
export const importProducts = async (
  rows: BulkProductRow[],
  options: BulkImportOptions
): Promise<BulkImportResult> => {
  const result: BulkImportResult = {
    success: false,
    totalRows: rows.length,
    successCount: 0,
    errorCount: 0,
    skippedCount: 0,
    errors: [],
    warnings: []
  };

  try {
    // Validate all rows first
    const validationErrors: BulkImportError[] = [];
    const validationWarnings: BulkImportWarning[] = [];

    rows.forEach((row, index) => {
      const validation = validateProductRow(row, index);

      if (!validation.isValid) {
        validation.errors.forEach(error => {
          validationErrors.push({
            row: index + 1,
            message: error
          });
        });
      }

      validation.warnings.forEach(warning => {
        validationWarnings.push({
          row: index + 1,
          message: warning
        });
      });
    });

    // Validate references (categories, farmers)
    const { errors: referenceErrors } = await validateReferences(rows);
    validationErrors.push(...referenceErrors);

    result.errors = validationErrors;
    result.warnings = validationWarnings;
    result.errorCount = validationErrors.length;

    // If validation only, return results
    if (options.validateOnly) {
      result.success = validationErrors.length === 0;
      return result;
    }

    // If there are validation errors and not skipping invalid rows, stop
    if (validationErrors.length > 0 && !options.skipInvalidRows) {
      return result;
    }

    // Process valid rows
    const validRows = rows.filter((_, index) => {
      const hasErrors = validationErrors.some(error => error.row === index + 1);
      return !hasErrors;
    });

    // Group rows by product (main product + its variants)
    const productGroups = new Map<string, { main: BulkProductRow; variants: BulkProductRow[] }>();

    validRows.forEach(row => {
      if (row.isVariant && row.parentBarcode) {
        // This is a variant row - group by parent barcode
        if (!productGroups.has(row.parentBarcode)) {
          productGroups.set(row.parentBarcode, { main: null as any, variants: [] });
        }
        productGroups.get(row.parentBarcode)!.variants.push(row);
      } else {
        // This is a main product row - group by barcode
        if (!productGroups.has(row.barcode)) {
          productGroups.set(row.barcode, { main: row, variants: [] });
        } else {
          productGroups.get(row.barcode)!.main = row;
        }
      }
    });

    // Process each product group
    for (const [barcode, group] of productGroups) {
      if (!group.main) {
        result.errors.push({
          row: 0,
          message: `No main product found for Barcode: ${barcode}`
        });
        continue;
      }

      try {
        // Check if product exists
        const existingProduct = await getProductByBarcode(barcode);

        if (existingProduct && !options.overwriteExisting) {
          result.skippedCount++;
          result.warnings.push({
            row: 0,
            message: `Product with Barcode ${barcode} already exists and overwrite is disabled`
          });
          continue;
        }

        // Convert row to product data
        const productData = await convertRowToProduct(group.main);

        let productId: string;

        if (existingProduct && options.overwriteExisting) {
          // Update existing product
          const updateData = mapProductToFirestore(productData);
          await updateDoc(doc(db, PRODUCTS_COLLECTION, existingProduct.id), {
            ...updateData,
            updatedAt: serverTimestamp()
          });
          productId = existingProduct.id;
        } else {
          // Create new product
          const createData = mapProductToFirestore(productData);
          const docRef = await addDoc(collection(db, PRODUCTS_COLLECTION), {
            ...createData,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          });
          productId = docRef.id;
        }

        // Process variants if any
        for (const variantRow of group.variants) {
          try {
            const variantData = convertRowToVariant(variantRow, productId, group.main.productId);
            await addProductVariant(productId, variantData);
          } catch (variantError) {
            console.error('Error adding variant:', variantError);
            result.errors.push({
              row: 0,
              message: `Failed to add variant for Barcode ${variantRow.barcode}: ${variantError}`
            });
          }
        }

        result.successCount++;
      } catch (error) {
        console.error('Error processing product:', error);
        result.errors.push({
          row: 0,
          message: `Failed to process product ${barcode}: ${error}`
        });
        result.errorCount++;
      }
    }

    result.success = result.errorCount === 0;
    return result;

  } catch (error) {
    console.error('Error importing products:', error);
    result.errors.push({
      row: 0,
      message: `Import failed: ${error}`
    });
    result.errorCount++;
    return result;
  }
};

// Export products to CSV format
export const exportProducts = async (options: BulkExportOptions): Promise<{ products: Product[]; variants: ProductVariant[] }> => {
  try {
    // Get all products
    let products = await getAllProducts();

    // Apply filters
    if (options.categoryFilter && options.categoryFilter !== 'All') {
      products = products.filter(product =>
        product.categoryIDs?.includes(options.categoryFilter!) ||
        product.category === options.categoryFilter
      );
    }

    if (options.statusFilter && options.statusFilter !== 'All') {
      products = products.filter(product => product.status === options.statusFilter);
    }

    // Get variants if needed
    let allVariants: ProductVariant[] = [];
    if (options.includeVariants) {
      const variantPromises = products
        .filter(product => product.hasVariants)
        .map(product => getProductVariants(product.id));

      const variantArrays = await Promise.all(variantPromises);
      allVariants = variantArrays.flat();
    }

    return { products, variants: allVariants };
  } catch (error) {
    console.error('Error exporting products:', error);
    throw error;
  }
};

// Get all farmers (helper function)
const getAllFarmers = async () => {
  try {
    const farmersRef = collection(db, 'Farmers');
    const querySnapshot = await getDocs(farmersRef);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting farmers:', error);
    return [];
  }
};

// Helper function to map product data to Firestore format
const mapProductToFirestore = (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): any => {
  return {
    name: product.name,
    description: product.description,
    categoryID: product.category,
    categoryIDs: product.categoryIDs || [],
    primaryCategoryID: product.primaryCategoryID || product.category,
    image: product.images && product.images.length > 0 ? product.images[0] : '',
    price: product.price,
    mrp: product.mrp || 0,
    maxQuantity: product.stock,
    minQuantity: 1,
    incrementalQuantity: 1,
    unit: product.unit,
    barcode: product.barcode,
    status: product.isVisible ? 'active' : 'inActive',
    keyword: [product.name.toLowerCase()],
    index: 0,
    branchCode: 'main',
    farmerId: product.farmerId || null,
    nutrition: product.nutrition || '',
    storageInstruction: product.storageInstruction || '',
    hasVariants: product.hasVariants || false,
    defaultVariantID: product.defaultVariantID || null,
    isPreOrder: product.isPreOrder || false,
    preOrderStartAt: product.preOrderStartAt ? Timestamp.fromDate(new Date(product.preOrderStartAt)) : null,
    preOrderEndAt: product.preOrderEndAt ? Timestamp.fromDate(new Date(product.preOrderEndAt)) : null,
    harvestOffsetDays: product.harvestOffsetDays || 0,
  };
};
