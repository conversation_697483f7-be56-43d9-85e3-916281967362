# ES Module Migration Quick Start

## ✅ Problem Solved!

Your scripts have been **converted from CommonJS to ES modules** to fix the `require()` compatibility errors.

## 🚀 Ready to Execute Commands

### 1. Test Setup (Recommended First)
```bash
node scripts/test-es-module-compatibility.js
```
**What it does:** Verifies ES modules work and Firebase connection is ready

### 2. Check Current Status
```bash
node scripts/check-product-id-status.js
```
**What it does:** Shows how many products need Product ID migration

### 3. Preview Migration (Safe)
```bash
node scripts/migrate-add-product-id.js --dry-run
```
**What it does:** Shows what will be changed WITHOUT making any changes

### 4. Execute Migration
```bash
node scripts/migrate-add-product-id.js --execute
```
**What it does:** Actually adds Product ID fields to your database

## 🔧 What Was Changed

### Before (CommonJS - Caused Errors):
```javascript
const admin = require('firebase-admin');
const serviceAccount = require('./serviceAccountKey.json');
```

### After (ES Modules - Works with your setup):
```javascript
import admin from 'firebase-admin';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const serviceAccount = JSON.parse(
  readFileSync(join(__dirname, 'serviceAccountKey.json'), 'utf8')
);
```

## 📋 Prerequisites Checklist

- [ ] **Firebase Admin SDK installed**: `npm install firebase-admin`
- [ ] **Service account key downloaded** and saved as `scripts/serviceAccountKey.json`
- [ ] **Project has "type": "module"** in package.json (✅ you already have this)
- [ ] **Node.js version 14+** (you have v22.14.0 ✅)

## 🎯 Expected Results

### Test Compatibility Output:
```
🧪 Testing ES Module Compatibility for Firebase Migration Scripts

📋 Test 1: Service Account Key
✅ PASS: serviceAccountKey.json found

📋 Test 2: Service Account Key Format
✅ PASS: Service account key is valid JSON

📋 Test 3: Service Account Fields
✅ PASS: All required service account fields present
📊 Project ID: vrisham-cad24

📋 Test 4: Firebase Admin SDK Initialization
✅ PASS: Firebase Admin SDK initialized successfully

📋 Test 5: Firestore Connection
✅ PASS: Firestore connection established
✅ PASS: Firestore query successful

🎉 ALL TESTS PASSED!
🚀 Ready to run migration scripts
```

### Migration Success Output:
```
Starting migration: Adding Product ID to existing products...
Found 25 products to migrate...

Processing product: abc123 (Organic Tomatoes)
  Assigning Product ID 1 to product abc123
  ✓ Successfully assigned Product ID 1 to product
  Found 2 variants for this product
    ✓ Successfully assigned productNumericId to variant

✅ Migration completed successfully!
Total products processed: 25
Products migrated (new Product ID): 25
Total variants processed: 8
Variants migrated (new productNumericId): 8
```

## 🔍 Verification

After migration, check Firebase Console:
1. Go to Firestore Database
2. Open any product document
3. You should see: `productId: 1` (or another number)
4. Check variants: should have `productNumericId: 1` (matching parent)

## 🆘 Troubleshooting

### Still getting `require()` errors?
- Make sure you're running the updated scripts
- Check that scripts start with `import` statements, not `require()`

### Firebase connection issues?
- Run the test script first: `node scripts/test-es-module-compatibility.js`
- Verify service account key is valid JSON
- Check Firebase project permissions

### Permission denied errors?
- Ensure service account has "Firebase Admin SDK Admin Service Agent" role
- Re-download service account key if needed

## 🎉 Success!

Once migration completes:
- ✅ All products will have unique Product IDs
- ✅ All variants will link to parent Product IDs  
- ✅ Frontend Product ID features will work
- ✅ Search by Product ID will function
- ✅ CSV import/export will include Product IDs

**Your Product ID system is now fully operational!** 🚀
