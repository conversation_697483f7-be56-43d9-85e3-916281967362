# Product ID Migration Setup Guide (ES Modules)

## Prerequisites

### 1. Install Firebase Admin SDK
```bash
npm install firebase-admin
```

### 1.1. ES Module Compatibility
✅ **Scripts converted to ES modules** - Compatible with your `"type": "module"` setup
✅ **No more `require()` errors** - All imports use ES module syntax

### 2. Get Firebase Service Account Key

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project (`vrisham-cad24`)
3. Go to **Project Settings** (gear icon) → **Service Accounts**
4. Click **"Generate new private key"**
5. Download the JSON file
6. Rename it to `serviceAccountKey.json`
7. Place it in the `scripts/` directory

**⚠️ SECURITY WARNING**: Never commit the service account key to version control!

### 3. Update .gitignore
Add this line to your `.gitignore` file:
```
scripts/serviceAccountKey.json
```

## Migration Steps

### Step 1: Test ES Module Compatibility
```bash
# Navigate to project root
cd /path/to/your/project

# Test that everything is set up correctly
node scripts/test-es-module-compatibility.js
```

### Step 2: Verify Setup
```bash
# Verify the service account key exists
ls scripts/serviceAccountKey.json
```

### Step 3: Run Dry Run (IMPORTANT!)
```bash
node scripts/migrate-add-product-id.js --dry-run
```

**Expected Output:**
```
DRY RUN: Preview of Product ID migration...
Found X products to check...
Products to migrate: Y
Next available Product ID: Z
```

### Step 4: Execute Migration
```bash
node scripts/migrate-add-product-id.js --execute
```

**Expected Output:**
```
Starting migration: Adding Product ID to existing products...
Found X products to migrate...
✓ Successfully assigned Product ID 1 to product
✓ Successfully assigned Product ID 2 to product
...
✅ Migration completed successfully!
```

### Step 5: Verify Results
1. Check Firebase Console → Firestore Database
2. Open any product document
3. Verify `productId` field exists with a numeric value
4. Check variants have `productNumericId` field

## Troubleshooting

### Error: "Cannot find module './serviceAccountKey.json'"
- Ensure the service account key file is in the `scripts/` directory
- Check the filename is exactly `serviceAccountKey.json`

### Error: "Permission denied"
- Verify the service account has Firestore read/write permissions
- Check the project ID in the script matches your Firebase project

### Error: "ENOENT: no such file or directory"
- Run the command from the project root directory
- Verify the script path: `scripts/migrate-add-product-id.js`

### No Products Found
- Verify you're connected to the correct Firebase project
- Check that products exist in the `Products` collection

## Post-Migration Verification

### 1. Check Database
- Open Firebase Console → Firestore
- Verify products have `productId` field
- Verify variants have `productNumericId` field

### 2. Test Frontend
- Create a new product (should auto-suggest Product ID)
- Edit existing product (should show existing Product ID)
- Search by Product ID in products list
- Test CSV import/export with Product ID

### 3. Verify Data Integrity
- All Product IDs should be unique
- All Product IDs should be positive integers
- Variants should share parent's Product ID

## Rollback (If Needed)

If something goes wrong, you can remove the Product ID fields:

```javascript
// Emergency rollback script (create if needed)
const admin = require('firebase-admin');
const serviceAccount = require('./serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

async function rollback() {
  const products = await db.collection('Products').get();

  for (const doc of products.docs) {
    await doc.ref.update({
      productId: admin.firestore.FieldValue.delete()
    });
  }

  console.log('Rollback completed');
}

// rollback();
```

## Support

If you encounter issues:
1. Check the console output for specific error messages
2. Verify Firebase permissions and project settings
3. Ensure all prerequisites are met
4. Test with a small subset of data first
