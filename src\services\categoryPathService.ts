import { getAllCategories } from './productService';

export interface CategoryPathMapping {
  path: string;
  categoryId: string;
  parentId?: string;
}

// Cache for category mappings to avoid repeated database calls
let categoryMappingsCache: CategoryPathMapping[] | null = null;
let lastCacheUpdate = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Get all category path mappings with caching
 */
export const getCategoryPathMappings = async (): Promise<CategoryPathMapping[]> => {
  const now = Date.now();
  
  // Return cached data if still valid
  if (categoryMappingsCache && (now - lastCacheUpdate) < CACHE_DURATION) {
    return categoryMappingsCache;
  }

  try {
    const categories = await getAllCategories();
    const mappings: CategoryPathMapping[] = [];

    categories.forEach(category => {
      // Add main category
      mappings.push({
        path: category.name,
        categoryId: category.id
      });

      // Add subcategories with full path
      if (category.subcategories && category.subcategories.length > 0) {
        category.subcategories.forEach(subcategory => {
          mappings.push({
            path: `${category.name}>${subcategory.name}`,
            categoryId: subcategory.id,
            parentId: category.id
          });
        });
      }
    });

    // Update cache
    categoryMappingsCache = mappings;
    lastCacheUpdate = now;

    return mappings;
  } catch (error) {
    console.error('Error getting category path mappings:', error);
    return [];
  }
};

/**
 * Convert category path to category ID
 * @param categoryPath - Path like "Vegetables>Leafy Greens" or "Fruits"
 * @returns Category ID or null if not found
 */
export const categoryPathToId = async (categoryPath: string): Promise<string | null> => {
  if (!categoryPath?.trim()) return null;

  const mappings = await getCategoryPathMappings();
  const mapping = mappings.find(m => m.path.toLowerCase() === categoryPath.toLowerCase());
  
  return mapping?.categoryId || null;
};

/**
 * Convert category ID to category path
 * @param categoryId - Category ID
 * @returns Category path or null if not found
 */
export const categoryIdToPath = async (categoryId: string): Promise<string | null> => {
  if (!categoryId?.trim()) return null;

  const mappings = await getCategoryPathMappings();
  const mapping = mappings.find(m => m.categoryId === categoryId);
  
  return mapping?.path || null;
};

/**
 * Validate category paths in bulk
 * @param categoryPaths - Array of category paths to validate
 * @returns Object with valid and invalid paths
 */
export const validateCategoryPaths = async (categoryPaths: string[]): Promise<{
  valid: { path: string; categoryId: string }[];
  invalid: string[];
}> => {
  const mappings = await getCategoryPathMappings();
  const valid: { path: string; categoryId: string }[] = [];
  const invalid: string[] = [];

  categoryPaths.forEach(path => {
    if (!path?.trim()) {
      invalid.push(path);
      return;
    }

    const mapping = mappings.find(m => m.path.toLowerCase() === path.toLowerCase());
    if (mapping) {
      valid.push({ path, categoryId: mapping.categoryId });
    } else {
      invalid.push(path);
    }
  });

  return { valid, invalid };
};

/**
 * Get all available category paths for template/validation
 */
export const getAllCategoryPaths = async (): Promise<string[]> => {
  const mappings = await getCategoryPathMappings();
  return mappings.map(m => m.path).sort();
};

/**
 * Clear the category mappings cache (useful when categories are updated)
 */
export const clearCategoryCache = (): void => {
  categoryMappingsCache = null;
  lastCacheUpdate = 0;
};
