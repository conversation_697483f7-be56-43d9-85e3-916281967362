/**
 * Test Script: Verify ES Module Compatibility
 * 
 * This script tests that the ES module conversion was successful and that
 * Firebase Admin SDK can be imported and initialized properly.
 * 
 * Run this before executing the migration to ensure everything works.
 */

import admin from 'firebase-admin';
import { readFileSync, existsSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function testESModuleCompatibility() {
  console.log('🧪 Testing ES Module Compatibility for Firebase Migration Scripts\n');
  
  try {
    // Test 1: Check if service account key exists
    console.log('📋 Test 1: Service Account Key');
    const serviceAccountPath = join(__dirname, 'serviceAccountKey.json');
    
    if (!existsSync(serviceAccountPath)) {
      console.log('❌ FAIL: serviceAccountKey.json not found in scripts/ directory');
      console.log('📝 Action needed: Download service account key from Firebase Console');
      console.log('🔗 URL: https://console.firebase.google.com/project/vrisham-cad24/settings/serviceaccounts/adminsdk');
      return false;
    }
    
    console.log('✅ PASS: serviceAccountKey.json found');
    
    // Test 2: Check if service account key is valid JSON
    console.log('\n📋 Test 2: Service Account Key Format');
    let serviceAccount;
    
    try {
      serviceAccount = JSON.parse(readFileSync(serviceAccountPath, 'utf8'));
      console.log('✅ PASS: Service account key is valid JSON');
    } catch (error) {
      console.log('❌ FAIL: Service account key is not valid JSON');
      console.log('📝 Action needed: Re-download service account key from Firebase Console');
      return false;
    }
    
    // Test 3: Check required fields in service account
    console.log('\n📋 Test 3: Service Account Fields');
    const requiredFields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email'];
    const missingFields = requiredFields.filter(field => !serviceAccount[field]);
    
    if (missingFields.length > 0) {
      console.log(`❌ FAIL: Missing required fields: ${missingFields.join(', ')}`);
      console.log('📝 Action needed: Re-download complete service account key');
      return false;
    }
    
    console.log('✅ PASS: All required service account fields present');
    console.log(`📊 Project ID: ${serviceAccount.project_id}`);
    console.log(`📧 Client Email: ${serviceAccount.client_email}`);
    
    // Test 4: Initialize Firebase Admin SDK
    console.log('\n📋 Test 4: Firebase Admin SDK Initialization');
    
    try {
      // Check if already initialized (avoid re-initialization error)
      if (admin.apps.length === 0) {
        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          projectId: 'vrisham-cad24',
          databaseURL: 'https://vrisham-cad24-default-rtdb.firebaseio.com'
        });
      }
      
      console.log('✅ PASS: Firebase Admin SDK initialized successfully');
    } catch (error) {
      console.log('❌ FAIL: Firebase Admin SDK initialization failed');
      console.log(`📝 Error: ${error.message}`);
      return false;
    }
    
    // Test 5: Test Firestore connection
    console.log('\n📋 Test 5: Firestore Connection');
    
    try {
      const db = admin.firestore();
      
      // Try to get a simple collection reference (doesn't actually query)
      const productsRef = db.collection('Products');
      console.log('✅ PASS: Firestore connection established');
      
      // Test basic query (just count, no data retrieval)
      const snapshot = await productsRef.limit(1).get();
      console.log(`✅ PASS: Firestore query successful (found ${snapshot.size} sample document)`);
      
    } catch (error) {
      console.log('❌ FAIL: Firestore connection failed');
      console.log(`📝 Error: ${error.message}`);
      return false;
    }
    
    // Test 6: Check Node.js version compatibility
    console.log('\n📋 Test 6: Node.js Version');
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    console.log(`📊 Node.js Version: ${nodeVersion}`);
    
    if (majorVersion < 14) {
      console.log('⚠️  WARNING: Node.js version is older than 14. ES modules work best with Node 14+');
    } else {
      console.log('✅ PASS: Node.js version supports ES modules');
    }
    
    // Test 7: Check package.json type
    console.log('\n📋 Test 7: Package.json Module Type');
    
    try {
      const packageJsonPath = join(__dirname, '..', 'package.json');
      if (existsSync(packageJsonPath)) {
        const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));
        
        if (packageJson.type === 'module') {
          console.log('✅ PASS: package.json has "type": "module"');
        } else {
          console.log('⚠️  WARNING: package.json does not specify "type": "module"');
          console.log('📝 This should be fine since scripts are using .js extension with ES imports');
        }
      } else {
        console.log('⚠️  WARNING: package.json not found in parent directory');
      }
    } catch (error) {
      console.log('⚠️  WARNING: Could not read package.json');
    }
    
    // All tests passed
    console.log('\n🎉 ALL TESTS PASSED!');
    console.log('\n✅ ES Module Compatibility: CONFIRMED');
    console.log('✅ Firebase Connection: WORKING');
    console.log('✅ Service Account: VALID');
    console.log('\n🚀 Ready to run migration scripts:');
    console.log('   node scripts/check-product-id-status.js');
    console.log('   node scripts/migrate-add-product-id.js --dry-run');
    console.log('   node scripts/migrate-add-product-id.js --execute');
    
    return true;
    
  } catch (error) {
    console.log('\n❌ UNEXPECTED ERROR during testing:');
    console.log(`📝 Error: ${error.message}`);
    console.log(`📝 Stack: ${error.stack}`);
    return false;
  }
}

// Run the test
testESModuleCompatibility()
  .then((success) => {
    if (success) {
      console.log('\n✨ Test completed successfully!');
      process.exit(0);
    } else {
      console.log('\n💥 Test failed. Please fix the issues above before running migration.');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('\n💥 Test crashed:', error);
    process.exit(1);
  });
