import Papa from 'papaparse';
import { BulkProductRow, CSV_HEADERS, ValidationResult, PreviewData, ProductIdConflict, BulkImportError, BulkImportWarning } from '../types/bulkProduct';
import { Product, ProductVariant } from '../types';
import { validateCategoryPaths, getAllCategoryPaths, categoryIdToPath } from '../services/categoryPathService';
import { checkProductIdExists } from '../services/productIdService';

// Parse CSV file to BulkProductRow array
export const parseCSVFile = (file: File): Promise<BulkProductRow[]> => {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      transformHeader: (header: string) => header.trim(),
      transform: (value: string, header: string) => {
        // Transform specific fields
        if (header === 'isVisible' || header === 'isPreOrder' || header === 'hasVariants' || header === 'isVariant') {
          return value.toLowerCase() === 'true' || value === '1';
        }
        if (header === 'price' || header === 'mrp' || header === 'stock' || header === 'harvestOffsetDays') {
          return parseFloat(value) || 0;
        }
        return value.trim();
      },
      complete: (results) => {
        if (results.errors.length > 0) {
          reject(new Error(`CSV parsing errors: ${results.errors.map(e => e.message).join(', ')}`));
        } else {
          resolve(results.data as BulkProductRow[]);
        }
      },
      error: (error) => {
        reject(error);
      }
    });
  });
};

// Generate CSV content from products and variants with proper category path resolution
export const generateCSVContent = async (products: Product[], variants: ProductVariant[]): Promise<string> => {
  const rows: BulkProductRow[] = [];

  // Create a map of variants by product ID for quick lookup
  const variantsByProduct = variants.reduce((acc, variant) => {
    if (!acc[variant.productId]) {
      acc[variant.productId] = [];
    }
    acc[variant.productId].push(variant);
    return acc;
  }, {} as Record<string, ProductVariant[]>);

  // Process each product
  for (const product of products) {
    // Resolve category path for the primary category
    let categoryPath = '';
    try {
      if (product.categoryIDs && product.categoryIDs.length > 0) {
        // Use the first category as primary category for categoryPath
        const primaryCategoryId = product.categoryIDs[0];
        const resolvedPath = await categoryIdToPath(primaryCategoryId);
        categoryPath = resolvedPath || '';
      } else if (product.category) {
        // Fallback to legacy category field
        const resolvedPath = await categoryIdToPath(product.category);
        categoryPath = resolvedPath || '';
      }
    } catch (error) {
      console.error('Error resolving category path for product:', product.name, error);
    }

    // Add main product row
    const productRow: BulkProductRow = {
      productId: product.productId || 0,
      name: product.name,
      description: product.description,
      price: product.price,
      mrp: product.mrp || 0,
      stock: product.stock,
      unit: product.unit,
      barcode: product.barcode,
      status: product.status,
      isVisible: product.isVisible,
      category: product.category || '', // Legacy field for backward compatibility
      categoryIDs: (product.categoryIDs || []).join(','), // Maintain for backward compatibility
      categoryPath: categoryPath, // Resolved human-readable path
      primaryCategoryID: product.primaryCategoryID || (product.categoryIDs && product.categoryIDs[0]) || '',
      farmerId: product.farmerId || '',
      nutrition: product.nutrition || '',
      storageInstruction: product.storageInstruction || '',
      isPreOrder: product.isPreOrder || false,
      preOrderStartAt: formatDateForCSV(product.preOrderStartAt),
      preOrderEndAt: formatDateForCSV(product.preOrderEndAt),
      harvestOffsetDays: product.harvestOffsetDays || 0,
      hasVariants: product.hasVariants || false,
      defaultVariantID: product.defaultVariantID || '',
      images: (product.images || []).join(',')
    };

    rows.push(productRow);

    // Add variant rows if product has variants
    if (product.hasVariants && variantsByProduct[product.id]) {
      for (const variant of variantsByProduct[product.id]) {
        const variantRow: BulkProductRow = {
          ...productRow, // Start with product data
          // Override with variant-specific data
          price: variant.price,
          mrp: variant.mrp || variant.price, // Use variant MRP or fallback to price
          stock: variant.stock,
          barcode: variant.barcode,
          status: variant.status === 'active' ? 'available' : 'out_of_stock',
          isVariant: true,
          parentBarcode: product.barcode,
          variationValues: JSON.stringify(variant.variationValues || {}),
          variantImage: variant.image || '', // Variant-specific image
          // Variant pre-order overrides
          isPreOrder: variant.isPreOrder !== null ? variant.isPreOrder : product.isPreOrder || false,
          preOrderStartAt: formatDateForCSV(variant.preOrderStartAt || product.preOrderStartAt),
          preOrderEndAt: formatDateForCSV(variant.preOrderEndAt || product.preOrderEndAt),
          harvestOffsetDays: variant.harvestOffsetDays !== null ? variant.harvestOffsetDays : (product.harvestOffsetDays || 0),
          // For variants, use variant image if available, otherwise use product images
          images: variant.image ? variant.image : (product.images || []).join(',')
        };

        rows.push(variantRow);
      }
    }
  }

  return Papa.unparse(rows, {
    header: true,
    columns: CSV_HEADERS
  });
};

// Generate CSV template with sample data (simplified format)
export const generateCSVTemplate = async (): Promise<string> => {
  // Get available category paths for examples
  let availablePaths: string[] = [];
  try {
    availablePaths = await getAllCategoryPaths();
  } catch (error) {
    console.error('Error getting category paths for template:', error);
  }

  const sampleCategoryPath1 = availablePaths.length > 0 ? availablePaths[0] : 'Vegetables>Leafy Greens';
  const sampleCategoryPath2 = availablePaths.length > 1 ? availablePaths[1] : 'Fruits>Seasonal';

  const sampleRows: BulkProductRow[] = [
    {
      productId: 1,
      name: 'Sample Product 1',
      description: 'This is a sample product description with detailed information',
      price: 100,
      mrp: 120,
      stock: 50,
      unit: 'kg',
      barcode: 'SAMPLE001',
      status: 'available',
      isVisible: true,
      category: '', // Legacy field - leave empty
      categoryIDs: '', // Legacy field - leave empty
      categoryPath: sampleCategoryPath1,
      primaryCategoryID: '', // Will be auto-resolved from categoryPath
      farmerId: '', // Optional - leave empty if no specific farmer
      nutrition: 'Rich in vitamins and minerals, high in fiber',
      storageInstruction: 'Store in cool, dry place away from direct sunlight',
      isPreOrder: false,
      preOrderStartAt: '',
      preOrderEndAt: '',
      harvestOffsetDays: 0,
      hasVariants: false,
      defaultVariantID: '',
      images: 'https://example.com/image1.jpg,https://example.com/image2.jpg'
    },
    {
      productId: 2,
      name: 'Product with Variants (Main)',
      description: 'This product has multiple size and color variants - this is the main product entry',
      price: 200,
      mrp: 250,
      stock: 0, // Main product stock is 0, variants have individual stock
      unit: 'piece',
      barcode: 'SAMPLE002',
      status: 'available',
      isVisible: true,
      category: '',
      categoryIDs: '',
      categoryPath: sampleCategoryPath2,
      primaryCategoryID: '',
      farmerId: '',
      nutrition: 'Organic and fresh, pesticide-free',
      storageInstruction: 'Refrigerate after opening, consume within 3 days',
      isPreOrder: false,
      preOrderStartAt: '',
      preOrderEndAt: '',
      harvestOffsetDays: 0,
      hasVariants: true,
      defaultVariantID: '',
      images: 'https://example.com/product2-main.jpg'
    },
    {
      productId: 2,
      name: 'Product with Variants (Main)',
      description: 'This product has multiple size and color variants - this is the main product entry',
      price: 180,
      mrp: 220,
      stock: 15,
      unit: 'piece',
      barcode: 'SAMPLE002-SM-RED',
      status: 'available',
      isVisible: true,
      category: '',
      categoryIDs: '',
      categoryPath: sampleCategoryPath2,
      primaryCategoryID: '',
      farmerId: '',
      nutrition: 'Organic and fresh, pesticide-free',
      storageInstruction: 'Refrigerate after opening, consume within 3 days',
      isPreOrder: false,
      preOrderStartAt: '',
      preOrderEndAt: '',
      harvestOffsetDays: 0,
      hasVariants: true,
      defaultVariantID: '',
      isVariant: true,
      parentBarcode: 'SAMPLE002',
      variationValues: '{"Size":"Small","Color":"Red"}',
      variantImage: 'https://example.com/product2-small-red.jpg',
      images: 'https://example.com/product2-small-red.jpg'
    },
    {
      productId: 2,
      name: 'Product with Variants (Main)',
      description: 'This product has multiple size and color variants - this is the main product entry',
      price: 200,
      mrp: 250,
      stock: 8,
      unit: 'piece',
      barcode: 'SAMPLE002-LG-BLUE',
      status: 'available',
      isVisible: true,
      category: '',
      categoryIDs: '',
      categoryPath: sampleCategoryPath2,
      primaryCategoryID: '',
      farmerId: '',
      nutrition: 'Organic and fresh, pesticide-free',
      storageInstruction: 'Refrigerate after opening, consume within 3 days',
      isPreOrder: false,
      preOrderStartAt: '',
      preOrderEndAt: '',
      harvestOffsetDays: 0,
      hasVariants: true,
      defaultVariantID: '',
      isVariant: true,
      parentBarcode: 'SAMPLE002',
      variationValues: '{"Size":"Large","Color":"Blue"}',
      variantImage: 'https://example.com/product2-large-blue.jpg',
      images: 'https://example.com/product2-large-blue.jpg'
    },
    {
      productId: 3,
      name: 'Pre-Order Product with Harvest Schedule',
      description: 'This product supports pre-ordering with specific harvest scheduling and delivery dates',
      price: 300,
      mrp: 350,
      stock: 100,
      unit: 'kg',
      barcode: 'SAMPLE003',
      status: 'coming_soon',
      isVisible: true,
      category: '',
      categoryIDs: '',
      categoryPath: sampleCategoryPath1,
      primaryCategoryID: '',
      farmerId: 'FARMER001',
      nutrition: 'Seasonal organic produce, high in antioxidants',
      storageInstruction: 'Store in refrigerator, consume within 5 days of delivery',
      isPreOrder: true,
      preOrderStartAt: '2024-01-01T00:00:00.000Z',
      preOrderEndAt: '2024-01-31T23:59:59.000Z',
      harvestOffsetDays: 3,
      hasVariants: false,
      defaultVariantID: '',
      images: 'https://example.com/product3-seasonal.jpg'
    }
  ];

  return Papa.unparse(sampleRows, {
    header: true,
    columns: [
      'productId',
      'name',
      'description',
      'price',
      'mrp',
      'stock',
      'unit',
      'barcode',
      'status',
      'isVisible',
      'categoryPath',
      'farmerId',
      'nutrition',
      'storageInstruction',
      'isPreOrder',
      'preOrderStartAt',
      'preOrderEndAt',
      'harvestOffsetDays',
      'hasVariants',
      'isVariant',
      'parentBarcode',
      'variationValues',
      'variantImage',
      'images'
    ]
  });
};

// Validate a single product row
export const validateProductRow = (row: BulkProductRow, rowIndex: number): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  if (!row.productId || row.productId <= 0) {
    errors.push('Product ID is required and must be a positive number');
  }

  if (!row.name?.trim()) {
    errors.push('Product name is required');
  }

  if (!row.barcode?.trim()) {
    errors.push('Barcode is required');
  }

  if (!row.unit?.trim()) {
    errors.push('Unit is required');
  }

  // Numeric validations
  if (isNaN(row.price) || row.price < 0) {
    errors.push('Price must be a valid positive number');
  }

  if (isNaN(row.stock) || row.stock < 0) {
    errors.push('Stock must be a valid non-negative number');
  }

  // Status validation
  if (!['available', 'out_of_stock', 'coming_soon'].includes(row.status)) {
    errors.push('Status must be one of: available, out_of_stock, coming_soon');
  }

  // Pre-order validation
  if (row.isPreOrder) {
    if (row.preOrderStartAt && row.preOrderEndAt) {
      const startDate = new Date(row.preOrderStartAt);
      const endDate = new Date(row.preOrderEndAt);

      if (isNaN(startDate.getTime())) {
        errors.push('Pre-order start date is invalid');
      }

      if (isNaN(endDate.getTime())) {
        errors.push('Pre-order end date is invalid');
      }

      if (startDate >= endDate) {
        errors.push('Pre-order start date must be before end date');
      }
    }
  }

  // Variant validation
  if (row.isVariant) {
    if (!row.parentBarcode?.trim()) {
      errors.push('Parent Barcode is required for variant products');
    }

    if (row.variationValues) {
      try {
        JSON.parse(row.variationValues);
      } catch {
        errors.push('Variation values must be valid JSON');
      }
    }
  }

  // Warnings
  if (!row.description?.trim()) {
    warnings.push('Product description is empty');
  }

  if (row.mrp && row.mrp < row.price) {
    warnings.push('MRP is less than selling price');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Helper function to format dates for CSV
const formatDateForCSV = (date: any): string => {
  if (!date) return '';

  try {
    // Handle Firestore timestamp
    if (date.toDate && typeof date.toDate === 'function') {
      return date.toDate().toISOString();
    }

    // Handle regular Date object or string
    return new Date(date).toISOString();
  } catch (error) {
    console.error('Error formatting date for CSV:', error);
    return '';
  }
};

// Download CSV file
export const downloadCSV = (content: string, filename: string): void => {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// Enhanced validation with category path support
export const validateProductRowEnhanced = async (row: BulkProductRow, rowIndex: number): Promise<ValidationResult> => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  if (!row.productId || row.productId <= 0) {
    errors.push('Product ID is required and must be a positive number');
  }

  if (!row.name?.trim()) {
    errors.push('Product name is required');
  }

  if (!row.barcode?.trim()) {
    errors.push('Barcode is required');
  }

  if (!row.unit?.trim()) {
    errors.push('Unit is required');
  }

  // Numeric validations
  if (isNaN(row.price) || row.price < 0) {
    errors.push('Price must be a valid positive number');
  }

  if (isNaN(row.stock) || row.stock < 0) {
    errors.push('Stock must be a valid non-negative number');
  }

  if (row.mrp && (isNaN(row.mrp) || row.mrp < 0)) {
    errors.push('MRP must be a valid positive number');
  }

  // Status validation
  if (!['available', 'out_of_stock', 'coming_soon'].includes(row.status)) {
    errors.push('Status must be one of: available, out_of_stock, coming_soon');
  }

  // Category path validation
  if (row.categoryPath?.trim()) {
    try {
      const { invalid } = await validateCategoryPaths([row.categoryPath]);
      if (invalid.length > 0) {
        errors.push(`Category path "${row.categoryPath}" does not exist`);
      }
    } catch (error) {
      warnings.push('Could not validate category path');
    }
  } else {
    warnings.push('Category path is empty');
  }

  // Pre-order validation
  if (row.isPreOrder) {
    if (row.preOrderStartAt && row.preOrderEndAt) {
      const startDate = new Date(row.preOrderStartAt);
      const endDate = new Date(row.preOrderEndAt);

      if (isNaN(startDate.getTime())) {
        errors.push('Pre-order start date is invalid');
      }

      if (isNaN(endDate.getTime())) {
        errors.push('Pre-order end date is invalid');
      }

      if (startDate >= endDate) {
        errors.push('Pre-order start date must be before end date');
      }
    }

    if (row.harvestOffsetDays !== undefined && (isNaN(row.harvestOffsetDays) || row.harvestOffsetDays < 0)) {
      errors.push('Harvest offset days must be a valid non-negative number');
    }
  }

  // Variant validation
  if (row.isVariant) {
    if (!row.parentBarcode?.trim()) {
      errors.push('Parent Barcode is required for variant products');
    }

    if (row.variationValues) {
      try {
        JSON.parse(row.variationValues);
      } catch {
        errors.push('Variation values must be valid JSON');
      }
    }
  }

  // Additional field validations
  if (!row.description?.trim()) {
    warnings.push('Product description is empty');
  }

  if (!row.nutrition?.trim()) {
    warnings.push('Nutrition information is empty');
  }

  if (!row.storageInstruction?.trim()) {
    warnings.push('Storage instructions are empty');
  }

  if (row.mrp && row.mrp < row.price) {
    warnings.push('MRP is less than selling price');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Generate preview data with validation and smart conflict detection
export const generatePreviewData = async (rows: BulkProductRow[], overwriteExisting: boolean = false): Promise<PreviewData> => {
  const validationErrors: BulkImportError[] = [];
  const validationWarnings: BulkImportWarning[] = [];
  const conflicts: ProductIdConflict[] = [];
  const productIdsSeen = new Set<number>();

  // Check for duplicate productIds within the CSV
  rows.forEach((row, index) => {
    if (row.productId && productIdsSeen.has(row.productId)) {
      conflicts.push({
        productId: row.productId,
        csvRowIndex: index,
        newProductName: row.name || 'Unknown Product',
        action: 'skip' // Default for CSV duplicates
      });
    } else if (row.productId) {
      productIdsSeen.add(row.productId);
    }
  });

  // Check for conflicts with existing products in database
  const uniqueProductIds = Array.from(productIdsSeen);
  let existingProductsCount = 0;

  for (const productId of uniqueProductIds) {
    try {
      const exists = await checkProductIdExists(productId);
      if (exists) {
        existingProductsCount++;
        const rowIndex = rows.findIndex(row => row.productId === productId);
        if (rowIndex !== -1) {
          conflicts.push({
            productId,
            csvRowIndex: rowIndex,
            existingProductName: 'Existing Product',
            newProductName: rows[rowIndex].name || 'Unknown Product',
            // Smart conflict resolution based on global setting
            action: overwriteExisting ? 'overwrite' : 'skip'
          });
        }
      }
    } catch (error) {
      console.error(`Error checking productId ${productId}:`, error);
    }
  }

  // Validate each row
  for (let i = 0; i < rows.length; i++) {
    try {
      const validation = await validateProductRowEnhanced(rows[i], i);

      if (!validation.isValid) {
        validation.errors.forEach(error => {
          validationErrors.push({
            row: i + 1,
            message: error
          });
        });
      }

      validation.warnings.forEach(warning => {
        validationWarnings.push({
          row: i + 1,
          message: warning
        });
      });
    } catch (error) {
      validationErrors.push({
        row: i + 1,
        message: `Validation error: ${error}`
      });
    }
  }

  // Add summary information for better UX
  const newProductsCount = rows.length - existingProductsCount;
  const csvDuplicatesCount = conflicts.filter(c => !c.existingProductName).length;

  return {
    isValid: validationErrors.length === 0,
    rows,
    conflicts,
    validationErrors,
    validationWarnings,
    variantPreviews: [], // Will be populated later if needed
    summary: {
      totalRows: rows.length,
      newProducts: newProductsCount,
      existingProducts: existingProductsCount,
      csvDuplicates: csvDuplicatesCount,
      willOverwrite: overwriteExisting ? existingProductsCount : 0
    }
  };
};

// Check for productId uniqueness within CSV and against database
export const checkProductIdUniqueness = async (rows: BulkProductRow[]): Promise<{
  duplicatesInCsv: { productId: number; rowIndexes: number[] }[];
  conflictsWithDatabase: ProductIdConflict[];
}> => {
  const productIdCounts = new Map<number, number[]>();
  const duplicatesInCsv: { productId: number; rowIndexes: number[] }[] = [];
  const conflictsWithDatabase: ProductIdConflict[] = [];

  // Track productId occurrences in CSV
  rows.forEach((row, index) => {
    if (row.productId && row.productId > 0) {
      if (!productIdCounts.has(row.productId)) {
        productIdCounts.set(row.productId, []);
      }
      productIdCounts.get(row.productId)!.push(index);
    }
  });

  // Find duplicates within CSV
  productIdCounts.forEach((indexes, productId) => {
    if (indexes.length > 1) {
      duplicatesInCsv.push({ productId, rowIndexes: indexes });
    }
  });

  // Check against database
  for (const [productId, indexes] of productIdCounts) {
    try {
      const exists = await checkProductIdExists(productId);
      if (exists) {
        conflictsWithDatabase.push({
          productId,
          csvRowIndex: indexes[0], // Use first occurrence
          existingProductName: 'Existing Product',
          newProductName: rows[indexes[0]].name || 'Unknown Product'
        });
      }
    } catch (error) {
      console.error(`Error checking productId ${productId}:`, error);
    }
  }

  return { duplicatesInCsv, conflictsWithDatabase };
};