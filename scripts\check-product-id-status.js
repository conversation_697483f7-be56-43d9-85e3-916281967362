/**
 * Status Check Script: Check Product ID implementation status
 *
 * This script checks the current status of Product ID implementation in your Firebase database.
 * It will tell you:
 * - How many products exist
 * - How many already have Product ID
 * - How many need migration
 * - Sample of existing data structure
 */

import admin from 'firebase-admin';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize Firebase Admin SDK for vrisham-cad24 project
const serviceAccount = JSON.parse(
  readFileSync(join(__dirname, 'serviceAccountKey.json'), 'utf8')
);

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: 'vrisham-cad24',
  databaseURL: 'https://vrisham-cad24-default-rtdb.firebaseio.com'
});

const db = admin.firestore();

async function checkProductIdStatus() {
  console.log('🔍 Checking Product ID implementation status...\n');

  try {
    // Get all products
    const productsSnapshot = await db.collection('Products').get();
    const totalProducts = productsSnapshot.size;

    console.log(`📊 PRODUCT ANALYSIS`);
    console.log(`Total products found: ${totalProducts}`);

    if (totalProducts === 0) {
      console.log('❌ No products found in the database.');
      return;
    }

    let productsWithId = 0;
    let productsWithoutId = 0;
    let maxProductId = 0;
    let sampleProducts = [];

    // Analyze each product
    for (const doc of productsSnapshot.docs) {
      const data = doc.data();

      if (data.productId) {
        productsWithId++;
        if (data.productId > maxProductId) {
          maxProductId = data.productId;
        }
      } else {
        productsWithoutId++;
      }

      // Collect sample data (first 3 products)
      if (sampleProducts.length < 3) {
        sampleProducts.push({
          id: doc.id,
          name: data.name || 'Unnamed',
          productId: data.productId || 'MISSING',
          barcode: data.barcode || 'No barcode'
        });
      }
    }

    console.log(`✅ Products with Product ID: ${productsWithId}`);
    console.log(`❌ Products without Product ID: ${productsWithoutId}`);
    console.log(`🔢 Highest Product ID found: ${maxProductId}`);

    // Check variants
    console.log(`\n📊 VARIANT ANALYSIS`);
    let totalVariants = 0;
    let variantsWithId = 0;
    let variantsWithoutId = 0;

    for (const productDoc of productsSnapshot.docs) {
      const variantsSnapshot = await db
        .collection('Products')
        .doc(productDoc.id)
        .collection('Variants')
        .get();

      totalVariants += variantsSnapshot.size;

      for (const variantDoc of variantsSnapshot.docs) {
        const variantData = variantDoc.data();
        if (variantData.productNumericId) {
          variantsWithId++;
        } else {
          variantsWithoutId++;
        }
      }
    }

    console.log(`Total variants found: ${totalVariants}`);
    console.log(`✅ Variants with productNumericId: ${variantsWithId}`);
    console.log(`❌ Variants without productNumericId: ${variantsWithoutId}`);

    // Show sample data
    console.log(`\n📋 SAMPLE PRODUCTS`);
    sampleProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name}`);
      console.log(`   Document ID: ${product.id}`);
      console.log(`   Product ID: ${product.productId}`);
      console.log(`   Barcode: ${product.barcode}`);
      console.log('');
    });

    // Migration status
    console.log(`🎯 MIGRATION STATUS`);
    if (productsWithoutId === 0 && variantsWithoutId === 0) {
      console.log(`✅ COMPLETE: All products and variants have Product IDs`);
      console.log(`🚀 Your system is ready to use Product ID features!`);
    } else {
      console.log(`⚠️  INCOMPLETE: Migration needed`);
      console.log(`📝 Next steps:`);
      console.log(`   1. Run: node scripts/migrate-add-product-id.js --dry-run`);
      console.log(`   2. Review the preview`);
      console.log(`   3. Run: node scripts/migrate-add-product-id.js --execute`);
    }

    // Recommendations
    console.log(`\n💡 RECOMMENDATIONS`);
    if (productsWithoutId > 0) {
      console.log(`• Run migration to add Product ID to ${productsWithoutId} products`);
    }
    if (variantsWithoutId > 0) {
      console.log(`• Migration will add productNumericId to ${variantsWithoutId} variants`);
    }
    if (maxProductId > 0) {
      console.log(`• Next Product ID will start from ${maxProductId + 1}`);
    } else {
      console.log(`• Product IDs will start from 1`);
    }

  } catch (error) {
    console.error('❌ Error checking status:', error);
    console.log('\n🔧 TROUBLESHOOTING:');
    console.log('• Ensure serviceAccountKey.json is in the scripts/ directory');
    console.log('• Verify Firebase project permissions');
    console.log('• Check internet connection');
  }
}

// Run the status check
checkProductIdStatus().then(() => {
  console.log('\n✨ Status check completed!');
  process.exit(0);
}).catch(console.error);
