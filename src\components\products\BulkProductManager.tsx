import React, { useState, useRef } from 'react';
import { Upload, Download, FileText, AlertCircle, CheckCircle, X, Loader2, Eye, AlertTriangle } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Badge } from '../ui/Badge';
import {
  BulkImportResult,
  BulkImportOptions,
  BulkExportOptions,
  BulkProductRow,
  PreviewData,
  ProductIdConflict
} from '../../types/bulkProduct';
import { parseCSVFile, generateCSVTemplate, generateCSVContent, downloadCSV, generatePreviewData } from '../../utils/csvUtils';
import { importProducts, exportProducts } from '../../services/bulkProductService';

interface BulkProductManagerProps {
  onImportComplete?: () => void;
}

export function BulkProductManager({ onImportComplete }: BulkProductManagerProps) {
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);
  const [importResult, setImportResult] = useState<BulkImportResult | null>(null);
  const [showImportModal, setShowImportModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [csvData, setCsvData] = useState<BulkProductRow[]>([]);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [conflictResolutions, setConflictResolutions] = useState<Record<number, 'overwrite' | 'skip'>>({});
  const [importOptions, setImportOptions] = useState<BulkImportOptions>({
    overwriteExisting: false,
    validateOnly: false,
    skipInvalidRows: true
  });
  const [exportOptions, setExportOptions] = useState<BulkExportOptions>({
    includeVariants: true,
    includeImages: true,
    categoryFilter: 'All',
    statusFilter: 'All'
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith('.csv')) {
      alert('Please select a CSV file');
      return;
    }

    try {
      setIsGeneratingPreview(true);
      const data = await parseCSVFile(file);
      setCsvData(data);

      // Generate preview data with validation and conflict detection
      const preview = await generatePreviewData(data);
      setPreviewData(preview);

      // Initialize conflict resolutions
      const initialResolutions: Record<number, 'overwrite' | 'skip'> = {};
      preview.conflicts.forEach(conflict => {
        initialResolutions[conflict.productId] = 'skip'; // Default to skip
      });
      setConflictResolutions(initialResolutions);

      setShowImportModal(true);
      setShowPreview(true);
    } catch (error) {
      console.error('Error parsing CSV:', error);
      alert('Error parsing CSV file. Please check the format and try again.');
    } finally {
      setIsGeneratingPreview(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handle import
  const handleImport = async () => {
    if (!csvData.length) return;

    try {
      setIsImporting(true);
      const result = await importProducts(csvData, importOptions);
      setImportResult(result);

      if (result.success && onImportComplete) {
        onImportComplete();
      }
    } catch (error) {
      console.error('Error importing products:', error);
      setImportResult({
        success: false,
        totalRows: csvData.length,
        successCount: 0,
        errorCount: 1,
        skippedCount: 0,
        errors: [{ row: 0, message: `Import failed: ${error}` }],
        warnings: []
      });
    } finally {
      setIsImporting(false);
    }
  };

  // Handle export
  const handleExport = async () => {
    try {
      setIsExporting(true);
      const { products, variants } = await exportProducts(exportOptions);
      const csvContent = generateCSVContent(products, variants);

      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `products-export-${timestamp}.csv`;

      downloadCSV(csvContent, filename);
      setShowExportModal(false);
    } catch (error) {
      console.error('Error exporting products:', error);
      alert('Error exporting products. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  // Download template
  const handleDownloadTemplate = async () => {
    try {
      const templateContent = await generateCSVTemplate();
      downloadCSV(templateContent, 'product-import-template.csv');
    } catch (error) {
      console.error('Error generating template:', error);
      alert('Error generating template. Please try again.');
    }
  };

  // Reset import state
  const resetImport = () => {
    setShowImportModal(false);
    setImportResult(null);
    setCsvData([]);
    setPreviewData(null);
    setShowPreview(false);
    setConflictResolutions({});
  };

  // Handle conflict resolution
  const handleConflictResolution = (productId: number, action: 'overwrite' | 'skip') => {
    setConflictResolutions(prev => ({
      ...prev,
      [productId]: action
    }));
  };

  // Proceed to import options after preview
  const proceedToImport = () => {
    setShowPreview(false);
  };

  return (
    <div className="space-y-4">
      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3">
        <Button
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          disabled={isGeneratingPreview || isImporting}
          className="shadow-sm hover:shadow transition-shadow"
        >
          {isGeneratingPreview ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Analyzing...
            </>
          ) : isImporting ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Importing...
            </>
          ) : (
            <>
              <Upload className="w-4 h-4 mr-2" />
              Bulk Upload
            </>
          )}
        </Button>

        <Button
          variant="outline"
          onClick={() => setShowExportModal(true)}
          disabled={isExporting}
          className="shadow-sm hover:shadow transition-shadow"
        >
          {isExporting ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Download className="w-4 h-4 mr-2" />
          )}
          Export All Products
        </Button>

        <Button
          variant="outline"
          onClick={handleDownloadTemplate}
          className="shadow-sm hover:shadow transition-shadow"
        >
          <FileText className="w-4 h-4 mr-2" />
          Download CSV Template
        </Button>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".csv"
        onChange={handleFileUpload}
        className="hidden"
      />

      {/* Import Modal */}
      {showImportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-6xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">
                  {showPreview ? 'Preview Import Data' : 'Import Products'}
                </h3>
                <Button variant="ghost" size="sm" onClick={resetImport}>
                  <X className="w-4 h-4" />
                </Button>
              </div>

              {!importResult ? (
                <>
                  {showPreview && previewData ? (
                    <div className="space-y-6">
                      {/* Summary */}
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <Eye className="w-5 h-5 text-blue-600" />
                          <h4 className="font-medium text-blue-800">Import Preview</h4>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>Total Rows: <Badge variant="secondary">{csvData.length}</Badge></div>
                          <div>Valid: <Badge variant="success">{previewData.isValid ? csvData.length - previewData.validationErrors.length : 0}</Badge></div>
                          <div>Errors: <Badge variant="destructive">{previewData.validationErrors.length}</Badge></div>
                          <div>Conflicts: <Badge variant="warning">{previewData.conflicts.length}</Badge></div>
                        </div>
                      </div>

                      {/* Conflicts Resolution */}
                      {previewData.conflicts.length > 0 && (
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <AlertTriangle className="w-5 h-5 text-yellow-600" />
                            <h4 className="font-medium text-yellow-800">Product ID Conflicts</h4>
                          </div>
                          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 space-y-3">
                            {previewData.conflicts.map((conflict, index) => (
                              <div key={index} className="flex items-center justify-between p-3 bg-white rounded border">
                                <div className="flex-1">
                                  <p className="font-medium">Product ID: {conflict.productId}</p>
                                  <p className="text-sm text-gray-600">
                                    {conflict.existingProductName ?
                                      `Conflicts with existing product: "${conflict.existingProductName}"` :
                                      `Duplicate in CSV`
                                    }
                                  </p>
                                  <p className="text-sm text-gray-600">New product: "{conflict.newProductName}"</p>
                                </div>
                                <div className="flex space-x-2">
                                  <Button
                                    size="sm"
                                    variant={conflictResolutions[conflict.productId] === 'overwrite' ? 'primary' : 'outline'}
                                    onClick={() => handleConflictResolution(conflict.productId, 'overwrite')}
                                  >
                                    Overwrite
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant={conflictResolutions[conflict.productId] === 'skip' ? 'primary' : 'outline'}
                                    onClick={() => handleConflictResolution(conflict.productId, 'skip')}
                                  >
                                    Skip
                                  </Button>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Validation Errors */}
                      {previewData.validationErrors.length > 0 && (
                        <div className="space-y-2">
                          <h5 className="font-medium text-red-800">Validation Errors:</h5>
                          <div className="max-h-40 overflow-y-auto space-y-1">
                            {previewData.validationErrors.slice(0, 10).map((error, index) => (
                              <div key={index} className="text-sm bg-red-100 border border-red-200 rounded p-2">
                                <span className="font-medium">Row {error.row}:</span> {error.message}
                              </div>
                            ))}
                            {previewData.validationErrors.length > 10 && (
                              <div className="text-sm text-gray-600 p-2">
                                ... and {previewData.validationErrors.length - 10} more errors
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Data Preview Table */}
                      <div className="space-y-2">
                        <h5 className="font-medium">Data Preview (First 5 rows):</h5>
                        <div className="overflow-x-auto border rounded-lg">
                          <table className="min-w-full text-sm">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-3 py-2 text-left">Product ID</th>
                                <th className="px-3 py-2 text-left">Name</th>
                                <th className="px-3 py-2 text-left">Price</th>
                                <th className="px-3 py-2 text-left">Stock</th>
                                <th className="px-3 py-2 text-left">Category Path</th>
                                <th className="px-3 py-2 text-left">Status</th>
                              </tr>
                            </thead>
                            <tbody>
                              {csvData.slice(0, 5).map((row, index) => (
                                <tr key={index} className="border-t">
                                  <td className="px-3 py-2">{row.productId}</td>
                                  <td className="px-3 py-2">{row.name}</td>
                                  <td className="px-3 py-2">₹{row.price}</td>
                                  <td className="px-3 py-2">{row.stock}</td>
                                  <td className="px-3 py-2">{row.categoryPath}</td>
                                  <td className="px-3 py-2">
                                    <Badge variant={row.status === 'available' ? 'success' : 'secondary'}>
                                      {row.status}
                                    </Badge>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>

                      <div className="flex justify-end space-x-3">
                        <Button variant="outline" onClick={resetImport}>
                          Cancel
                        </Button>
                        <Button
                          variant="primary"
                          onClick={proceedToImport}
                          disabled={previewData.validationErrors.length > 0}
                        >
                          Continue to Import
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <p className="text-sm text-blue-800">
                          Found <strong>{csvData.length}</strong> rows in the CSV file.
                        </p>
                      </div>

                      {/* Import Options */}
                      <div className="space-y-3">
                        <h4 className="font-medium">Import Options</h4>

                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={importOptions.overwriteExisting}
                            onChange={(e) => setImportOptions(prev => ({
                              ...prev,
                              overwriteExisting: e.target.checked
                            }))}
                            className="rounded border-gray-300"
                          />
                          <span className="text-sm">Overwrite existing products (match by Product ID)</span>
                        </label>

                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={importOptions.validateOnly}
                            onChange={(e) => setImportOptions(prev => ({
                              ...prev,
                              validateOnly: e.target.checked
                            }))}
                            className="rounded border-gray-300"
                          />
                          <span className="text-sm">Validate only (don't import)</span>
                        </label>

                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={importOptions.skipInvalidRows}
                            onChange={(e) => setImportOptions(prev => ({
                              ...prev,
                              skipInvalidRows: e.target.checked
                            }))}
                            className="rounded border-gray-300"
                          />
                          <span className="text-sm">Skip invalid rows and continue</span>
                        </label>
                      </div>

                      <div className="flex justify-end space-x-3">
                        <Button variant="outline" onClick={resetImport}>
                          Cancel
                        </Button>
                        <Button
                          variant="primary"
                          onClick={handleImport}
                          disabled={isImporting}
                        >
                          {isImporting ? (
                            <>
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              {importOptions.validateOnly ? 'Validating...' : 'Importing...'}
                            </>
                          ) : (
                            importOptions.validateOnly ? 'Validate' : 'Import'
                          )}
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="space-y-4">
                  {/* Import Results */}
                  <div className={`border rounded-lg p-4 ${
                    importResult.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                  }`}>
                    <div className="flex items-center space-x-2 mb-2">
                      {importResult.success ? (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      ) : (
                        <AlertCircle className="w-5 h-5 text-red-600" />
                      )}
                      <h4 className={`font-medium ${
                        importResult.success ? 'text-green-800' : 'text-red-800'
                      }`}>
                        {importResult.success ? 'Import Successful' : 'Import Failed'}
                      </h4>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>Total Rows: <Badge variant="secondary">{importResult.totalRows}</Badge></div>
                      <div>Successful: <Badge variant="success">{importResult.successCount}</Badge></div>
                      <div>Errors: <Badge variant="destructive">{importResult.errorCount}</Badge></div>
                      <div>Skipped: <Badge variant="warning">{importResult.skippedCount}</Badge></div>
                    </div>
                  </div>

                  {/* Errors */}
                  {importResult.errors.length > 0 && (
                    <div className="space-y-2">
                      <h5 className="font-medium text-red-800">Errors:</h5>
                      <div className="max-h-40 overflow-y-auto space-y-1">
                        {importResult.errors.map((error, index) => (
                          <div key={index} className="text-sm bg-red-100 border border-red-200 rounded p-2">
                            <span className="font-medium">Row {error.row}:</span> {error.message}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Warnings */}
                  {importResult.warnings.length > 0 && (
                    <div className="space-y-2">
                      <h5 className="font-medium text-yellow-800">Warnings:</h5>
                      <div className="max-h-40 overflow-y-auto space-y-1">
                        {importResult.warnings.map((warning, index) => (
                          <div key={index} className="text-sm bg-yellow-100 border border-yellow-200 rounded p-2">
                            <span className="font-medium">Row {warning.row}:</span> {warning.message}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end">
                    <Button variant="primary" onClick={resetImport}>
                      Close
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </Card>
        </div>
      )}

      {/* Export Modal */}
      {showExportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Export Products</h3>
                <Button variant="ghost" size="sm" onClick={() => setShowExportModal(false)}>
                  <X className="w-4 h-4" />
                </Button>
              </div>

              <div className="space-y-4">
                <div className="space-y-3">
                  <h4 className="font-medium">Export Options</h4>

                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={exportOptions.includeVariants}
                      onChange={(e) => setExportOptions(prev => ({
                        ...prev,
                        includeVariants: e.target.checked
                      }))}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Include product variants</span>
                  </label>

                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={exportOptions.includeImages}
                      onChange={(e) => setExportOptions(prev => ({
                        ...prev,
                        includeImages: e.target.checked
                      }))}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">Include image URLs</span>
                  </label>
                </div>

                <div className="flex justify-end space-x-3">
                  <Button variant="outline" onClick={() => setShowExportModal(false)}>
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleExport}
                    disabled={isExporting}
                  >
                    {isExporting ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Exporting...
                      </>
                    ) : (
                      'Export'
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}
